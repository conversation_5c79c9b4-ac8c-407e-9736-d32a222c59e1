#!/usr/bin/env python3
"""
Тестовый парсер CPA Quebec Directory - с выбором категории "Individuals"
"""
import asyncio
import json
import logging
import os
from datetime import datetime
from pathlib import Path

import aiohttp
from dotenv import load_dotenv
from playwright.async_api import async_playwright

# Загружаем переменные окружения
load_dotenv()

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger("test_quebec_parser")

# Константы
BASE_URL = "https://cpaquebec.ca/en/find-a-cpa/cpa-directory/"
OUTPUT_DIR = Path("output")
ANTICAPTCHA_KEY = os.getenv("ANTICAPTCHA_API_KEY")

# Глобальная сессия для Anti-Captcha
anticaptcha_session = None

async def get_anticaptcha_session():
    """Получает или создает сессию для Anti-Captcha API"""
    global anticaptcha_session
    if anticaptcha_session is None or anticaptcha_session.closed:
        anticaptcha_session = aiohttp.ClientSession()
    return anticaptcha_session

async def anticaptcha_request(method: str, **payload):
    """Выполняет запрос к Anti-Captcha API"""
    url = f"https://api.anti-captcha.com/{method}"
    session = await get_anticaptcha_session()

    if "clientKey" not in payload and ANTICAPTCHA_KEY:
        payload["clientKey"] = ANTICAPTCHA_KEY

    logger.debug(f"Anti-Captcha request: {method}, payload_keys: {list(payload.keys())}")
    try:
        async with session.post(url, json=payload, timeout=30) as resp:
            # Логируем HTTP статус
            logger.debug(f"Anti-Captcha response status ({method}): {resp.status}")

            response_json = await resp.json()
            # Логируем полный ответ API
            logger.debug(f"Anti-Captcha full response ({method}): {json.dumps(response_json, indent=2)}")

            # Проверяем на ошибки в ответе JSON, даже если статус HTTP 200
            if response_json.get("errorId", 0) != 0:
                error_code = response_json.get("errorCode", "UNKNOWN_API_ERROR")
                error_description = response_json.get("errorDescription", "No description from API.")
                logger.error(f"Anti-Captcha API error in response ({method}): {error_code} - {error_description}")
                # Возвращаем структуру ошибки, чтобы ее можно было обработать выше
                return {"errorId": response_json["errorId"], "errorCode": error_code, "errorDescription": error_description}

            resp.raise_for_status() # Это вызовет исключение для HTTP ошибок 4xx/5xx
            return response_json
    except aiohttp.ClientResponseError as e:
        logger.error(f"Anti-Captcha HTTP error ({method}, status={e.status}): {e.message}")
        error_body = await e.response.text() if e.response else "No response body"
        logger.error(f"Anti-Captcha HTTP error body: {error_body}")
        return {"errorId": 1, "errorCode": f"HTTP_{e.status}", "errorDescription": f"{e.message} - Body: {error_body[:200]}"}
    except asyncio.TimeoutError:
        logger.error(f"Anti-Captcha API timeout ({method})")
        return {"errorId": 1, "errorCode": "TIMEOUT_ERROR", "errorDescription": "API request timed out"}
    except Exception as e:
        logger.error(f"Generic error in Anti-Captcha API request ({method}): {e}", exc_info=True)
        return {"errorId": 1, "errorCode": "REQUEST_EXCEPTION", "errorDescription": str(e)}

async def check_anticaptcha_balance():
    """Проверяет баланс Anti-Captcha."""
    if not ANTICAPTCHA_KEY:
        logger.debug("ANTICAPTCHA_KEY не установлен, проверка баланса пропущена.")
        return True # Считаем, что все в порядке, если ключ не используется

    logger.info("Проверка баланса Anti-Captcha...")
    response = await anticaptcha_request("getBalance")

    if response.get("errorId", 0) != 0:
        logger.error(f"Не удалось проверить баланс Anti-Captcha: {response.get('errorDescription')}")
        return False # Ошибка при проверке баланса

    balance = response.get("balance")
    if balance is not None:
        logger.info(f"Текущий баланс Anti-Captcha: ${balance}")
        if float(balance) < 0.1: # Порог предупреждения
            logger.warning(f"НИЗКИЙ БАЛАНС Anti-Captcha: ${balance}. Рекомендуется пополнить.")
        return True
    else:
        logger.error("Не удалось получить значение баланса из ответа Anti-Captcha.")
        return False

async def select_category(page, category_name="Individuals"):
    """Выбирает категорию на странице"""
    logger.info(f"Выбираем категорию: {category_name}")

    # Сначала снимаем все чекбоксы
    await page.evaluate("""
        () => {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    checkbox.checked = false;
                    checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log('Снят чекбокс:', checkbox.id || checkbox.name);
                }
            });
        }
    """)

    await page.wait_for_timeout(2000)

    # Выбираем нужную категорию
    category_selected = await page.evaluate(f"""
        (categoryName) => {{
            // Ищем по тексту метки
            const labels = document.querySelectorAll('label');
            for (const label of labels) {{
                if (label.textContent.includes(categoryName)) {{
                    console.log('Найдена метка для категории:', categoryName);

                    // Если есть for атрибут
                    if (label.htmlFor) {{
                        const checkbox = document.getElementById(label.htmlFor);
                        if (checkbox) {{
                            checkbox.checked = true;
                            checkbox.dispatchEvent(new Event('change', {{ bubbles: true }}));
                            console.log('Отмечен чекбокс через ID:', checkbox.id);
                            return true;
                        }}
                    }}

                    // Ищем чекбокс внутри метки
                    const checkbox = label.querySelector('input[type="checkbox"]');
                    if (checkbox) {{
                        checkbox.checked = true;
                        checkbox.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        console.log('Отмечен чекбокс внутри метки');
                        return true;
                    }}
                }}
            }}

            // Альтернативный поиск по ID содержащему категорию
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            for (const checkbox of checkboxes) {{
                if (checkbox.id && checkbox.id.includes(categoryName)) {{
                    checkbox.checked = true;
                    checkbox.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    console.log('Отмечен чекбокс по ID содержащему', categoryName + ':', checkbox.id);
                    return true;
                }}
                if (checkbox.name && checkbox.name.includes(categoryName)) {{
                    checkbox.checked = true;
                    checkbox.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    console.log('Отмечен чекбокс по name содержащему', categoryName + ':', checkbox.name);
                    return true;
                }}
            }}

            console.error('Не удалось найти чекбокс для категории:', categoryName);
            return false;
        }}
    """, category_name)

    if category_selected:
        # Также отмечаем чекбокс "Accepting new clients"
        accepting_clients_selected = await page.evaluate("""
            () => {
                const labels = document.querySelectorAll('label');
                for (const label of labels) {
                    if (label.textContent.includes('Accepting new clients')) {
                        console.log('Найдена метка "Accepting new clients"');

                        // Если есть for атрибут
                        if (label.htmlFor) {
                            const checkbox = document.getElementById(label.htmlFor);
                            if (checkbox) {
                                checkbox.checked = true;
                                checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                                console.log('Отмечен чекбокс "Accepting new clients" через ID:', checkbox.id);
                                return true;
                            }
                        }

                        // Ищем чекбокс внутри метки
                        const checkbox = label.querySelector('input[type="checkbox"]');
                        if (checkbox) {
                            checkbox.checked = true;
                            checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                            console.log('Отмечен чекбокс "Accepting new clients" внутри метки');
                            return true;
                        }
                    }
                }

                console.log('Чекбокс "Accepting new clients" не найден');
                return false;
            }
        """)

        if accepting_clients_selected:
            logger.info("Чекбокс 'Accepting new clients' также отмечен")
        else:
            logger.warning("Не удалось отметить чекбокс 'Accepting new clients'")

        # КРИТИЧЕСКИ ВАЖНО: Заполняем текстовые поля!
        # URL содержит Length=8, возможно требуется минимум 8 символов
        logger.info("🔍 ЗАПОЛНЯЕМ ТЕКСТОВЫЕ ПОЛЯ (возможно, обязательные)...")

        fields_filled = await page.evaluate("""
            () => {
                let filledCount = 0;

                // 1. Ищем поле фамилии CPA
                const lastNameSelectors = [
                    'input[name*="lastname" i]',
                    'input[name*="last" i]',
                    'input[id*="lastname" i]',
                    'input[id*="last" i]',
                    'input[placeholder*="last name" i]'
                ];

                for (const selector of lastNameSelectors) {
                    const field = document.querySelector(selector);
                    if (field && field.type === 'text') {
                        field.value = 'Anderson'; // 8 символов
                        field.dispatchEvent(new Event('input', { bubbles: true }));
                        field.dispatchEvent(new Event('change', { bubbles: true }));
                        console.log('✅ Заполнено поле фамилии:', field.name || field.id, '= Anderson');
                        filledCount++;
                        break;
                    }
                }

                // 2. Ищем поле имени CPA
                const firstNameSelectors = [
                    'input[name*="firstname" i]',
                    'input[name*="first" i]',
                    'input[id*="firstname" i]',
                    'input[id*="first" i]',
                    'input[placeholder*="first name" i]'
                ];

                for (const selector of firstNameSelectors) {
                    const field = document.querySelector(selector);
                    if (field && field.type === 'text') {
                        field.value = 'Michael'; // 7 символов
                        field.dispatchEvent(new Event('input', { bubbles: true }));
                        field.dispatchEvent(new Event('change', { bubbles: true }));
                        console.log('✅ Заполнено поле имени:', field.name || field.id, '= Michael');
                        filledCount++;
                        break;
                    }
                }

                // 3. Ищем поле города
                const citySelectors = [
                    'input[name*="city" i]',
                    'input[name*="ville" i]',
                    'input[id*="city" i]',
                    'input[id*="ville" i]',
                    'input[placeholder*="city" i]',
                    'select[name*="city" i]',
                    'select[name*="ville" i]'
                ];

                for (const selector of citySelectors) {
                    const field = document.querySelector(selector);
                    if (field) {
                        console.log('Найдено поле города:', selector, field.name || field.id);

                        if (field.tagName === 'SELECT') {
                            // Если это выпадающий список, выбираем первый доступный город
                            const options = field.querySelectorAll('option');
                            if (options.length > 1) {
                                field.selectedIndex = 1; // Выбираем первый реальный город (не пустой)
                                field.dispatchEvent(new Event('change', { bubbles: true }));
                                console.log('✅ Выбран город из списка:', field.options[field.selectedIndex].text);
                                filledCount++;
                                break;
                            }
                        } else if (field.type === 'text') {
                            // Если это текстовое поле, вводим Montreal (8 символов)
                            field.value = 'Montreal';
                            field.dispatchEvent(new Event('input', { bubbles: true }));
                            field.dispatchEvent(new Event('change', { bubbles: true }));
                            console.log('✅ Введен город: Montreal');
                            filledCount++;
                            break;
                        }
                    }
                }

                console.log(`📝 Заполнено полей: ${filledCount}`);
                return filledCount;
            }
        """)

        if fields_filled > 0:
            logger.info(f"✅ Заполнено {fields_filled} текстовых полей")
        else:
            logger.warning("⚠️ Не удалось заполнить текстовые поля - возможно, они не найдены")

    if category_selected:
        logger.info(f"Категория '{category_name}' успешно выбрана")
        await page.wait_for_timeout(3000)  # Ждем обновления формы
        return True
    else:
        logger.error(f"Не удалось выбрать категорию '{category_name}'")
        return False

async def solve_captcha_simple(page):
    """Простое решение капчи через Anti-Captcha с ужесточенными проверками"""
    if not ANTICAPTCHA_KEY:
        logger.warning("ANTICAPTCHA_API_KEY не установлен, используем ручное решение")
        input("Решите капчу вручную и нажмите Enter для продолжения...")
        return True

    # Проверка баланса перед началом
    if not await check_anticaptcha_balance():
        logger.error("Проверка баланса Anti-Captcha не удалась или баланс слишком низкий. Решение капчи прервано.")
        return False

    # Механизм повторных попыток
    max_retries = 3
    for attempt_num in range(max_retries): # Изменено имя переменной
        logger.info(f"Попытка решения капчи {attempt_num + 1}/{max_retries}...")
        try:
            # Сначала проверяем, есть ли капча на странице
            captcha_present = await page.evaluate("""
                () => {
                    const recaptchaDiv = document.querySelector('.g-recaptcha');
                    const recaptchaIframes = document.querySelectorAll('iframe[src*="recaptcha"]');
                    return recaptchaDiv !== null || recaptchaIframes.length > 0;
                }
            """)

            if not captcha_present:
                logger.info("Капча не найдена на странице")
                return True

            logger.info("Капча обнаружена на странице")

            # Очищаем любые существующие токены
            await page.evaluate("""
                () => {
                    const existingToken = document.querySelector('#g-recaptcha-response');
                    if (existingToken) {
                        existingToken.value = '';
                        console.log('Очищен существующий токен');
                    }
                }
            """)

            existing_token = await page.evaluate("""
                () => {
                    const response = document.querySelector('#g-recaptcha-response');
                    return response ? response.value : null;
                }
            """)

            if existing_token and len(existing_token) > 50:
                logger.warning(f"ВНИМАНИЕ: Найден существующий токен длиной {len(existing_token)}! Возможно кэш.")
                logger.info("Принудительно очищаем токен...")
                await page.evaluate("""
                    () => {
                        const response = document.querySelector('#g-recaptcha-response');
                        if (response) {
                            response.remove();
                        }
                        const hiddenFields = document.querySelectorAll('input[name="g-recaptcha-response"]');
                        hiddenFields.forEach(field => field.remove());
                    }
                """)

            sitekey = await page.evaluate("""
                () => {
                    const recaptchaDiv = document.querySelector('.g-recaptcha');
                    if (recaptchaDiv && recaptchaDiv.getAttribute('data-sitekey')) {
                        return recaptchaDiv.getAttribute('data-sitekey');
                    }
                    const iframes = document.querySelectorAll('iframe[src*="recaptcha"]');
                    for (const iframe of iframes) {
                        const src = iframe.src;
                        const match = src.match(/k=([^&]+)/);
                        if (match) {
                            return match[1];
                        }
                    }
                    return null;
                }
            """)

            if not sitekey:
                logger.error("Не удалось найти sitekey, переход к следующей попытке...")
                await asyncio.sleep(2)
                continue # К следующей основной попытке

            logger.info(f"Найден sitekey: {sitekey[:10]}...")

            checkbox_state = await page.evaluate("""
                () => {
                    const iframes = document.querySelectorAll('iframe[src*="api2/anchor"]');
                    if (iframes.length > 0) {
                        try {
                            const iframe = iframes[0];
                            const frame = iframe.contentDocument || iframe.contentWindow.document;
                            const checkbox = frame.querySelector('#recaptcha-anchor');
                            return checkbox ? checkbox.getAttribute('aria-checked') : 'unknown';
                        } catch (e) {
                            return 'cross-origin';
                        }
                    }
                    return 'no-iframe';
                }
            """)
            logger.info(f"Состояние чекбокса reCAPTCHA: {checkbox_state}")

            logger.info("Отправляем задачу в Anti-Captcha...")
            task_payload = {
                "task": {
                    "type": "NoCaptchaTaskProxyless",
                    "websiteURL": BASE_URL,
                    "websiteKey": sitekey,
                }
            }

            response_create_task = await anticaptcha_request("createTask", **task_payload)
            if response_create_task.get("errorId", 0) != 0:
                logger.error(f"Ошибка создания задачи Anti-Captcha: {response_create_task.get('errorDescription')}, переход к следующей попытке...")
                await asyncio.sleep(2)
                continue # К следующей основной попытке

            task_id = response_create_task.get("taskId")
            if not task_id:
                logger.error("Не удалось создать задачу Anti-Captcha (нет taskId в ответе), переход к следующей попытке...")
                await asyncio.sleep(2)
                continue # К следующей основной попытке

            logger.info(f"Задача Anti-Captcha создана, ID: {task_id}")

            start_time_task = asyncio.get_event_loop().time() # Используем другое имя переменной
            await asyncio.sleep(10)

            task_solved_successfully = False
            for poll_attempt in range(50): # Изменено имя переменной
                current_time = asyncio.get_event_loop().time()
                elapsed = current_time - start_time_task

                response_get_result = await anticaptcha_request("getTaskResult", taskId=task_id)

                if response_get_result.get("errorId", 0) != 0:
                    logger.error(f"Ошибка получения результата задачи Anti-Captcha: {response_get_result.get('errorDescription')}")
                    if response_get_result.get("errorCode") == "ERROR_KEY_DOES_NOT_EXIST":
                        logger.critical("Ключ Anti-Captcha не существует. Прерывание всех попыток.")
                        return False # Прерываем все
                    # Для других ошибок просто ждем и пробуем еще раз получить результат
                    await asyncio.sleep(5)
                    continue # К следующей попытке получения результата

                if response_get_result.get("status") == "ready":
                    solution = response_get_result.get("solution")
                    token = solution.get("gRecaptchaResponse") if solution else None

                    if token and len(token) > 50:
                        logger.info(f"Получен токен от Anti-Captcha за {elapsed:.1f}с, длина: {len(token)}")
                        if not token.startswith("03"): # Эта проверка может быть не всегда актуальна
                             logger.warning(f"Токен имеет формат, отличный от ожидаемого (не начинается с '03'): {token[:20]}...")

                        # Вставляем токен и делаем кнопки активными
                        await page.evaluate(f"""
                            (token) => {{
                                let response_el = document.querySelector('#g-recaptcha-response');
                                if (!response_el) {{
                                    response_el = document.createElement('textarea');
                                    response_el.id = 'g-recaptcha-response';
                                    response_el.name = 'g-recaptcha-response';
                                    response_el.style.display = 'none';
                                    document.body.appendChild(response_el);
                                }}
                                response_el.value = token;
                                response_el.dispatchEvent(new Event('change', {{ bubbles: true }}));

                                // Принудительно активируем все кнопки отправки
                                const buttons = document.querySelectorAll('button[type="submit"], input[type="submit"]');
                                buttons.forEach(btn => {{
                                    btn.disabled = false;
                                    btn.removeAttribute('disabled');
                                    // Применяем стили, как будто кнопка активна
                                    btn.classList.remove('disabled');
                                    btn.style.opacity = '1';
                                    btn.style.cursor = 'pointer';
                                    console.log('Активирована кнопка:', btn);
                                }});

                                // Если форма содержит reCAPTCHA, отмечаем ее как решенную
                                const recaptchaDiv = document.querySelector('.g-recaptcha');
                                if (recaptchaDiv) {{
                                    recaptchaDiv.dataset.captchaResolved = 'true';
                                }}

                                // Удаляем любые блокировки для формы, если они есть
                                const forms = document.querySelectorAll('form');
                                forms.forEach(form => {{
                                    if (form.getAttribute('data-captcha-required') === 'true') {{
                                        form.setAttribute('data-captcha-required', 'false');
                                    }}
                                }});

                                console.log('Новый токен капчи вставлен, длина:', token.length);
                            }}
                        """, token)

                        await page.wait_for_timeout(2000)
                        inserted_token = await page.evaluate("""
                            () => {
                                const response = document.querySelector('#g-recaptcha-response');
                                return response ? response.value : null;
                            }
                        """)

                        if inserted_token and len(inserted_token) > 50:
                            logger.info(f"Токен успешно вставлен и проверен, длина: {len(inserted_token)}")

                            # Проверяем статус кнопок после вставки токена
                            buttons_state = await page.evaluate("""
                                () => {
                                    const buttons = Array.from(document.querySelectorAll('button[type="submit"], input[type="submit"]'));
                                    return buttons.map(btn => ({
                                        text: btn.textContent || btn.value || 'КНОПКА',
                                        disabled: btn.disabled,
                                        visible: btn.offsetParent !== null
                                    }));
                                }
                            """)
                            logger.info(f"Состояние кнопок после вставки токена: {buttons_state}")

                            # Если токен вставлен, считаем капчу решенной
                            logger.info("КАПЧА РЕШЕНА! Токен вставлен, кнопки активированы.")
                            task_solved_successfully = True
                            break # Успешное решение, выход из цикла ожидания результата
                        else:
                            logger.error("Токен не найден после вставки! Переход к следующей основной попытке...")
                            await asyncio.sleep(2)
                            break # Выход из цикла ожидания результата, переход к следующей основной попытке

                elif response_get_result.get("status") == "processing":
                    logger.debug(f"Задача {task_id} обрабатывается... (попытка {poll_attempt + 1}, время: {elapsed:.1f}с)")
                else:
                    logger.warning(f"Неожиданный статус от Anti-Captcha: {response_get_result.get('status')}")

                await asyncio.sleep(5)

            if task_solved_successfully:
                return True # Капча успешно решена в этой основной попытке
            else:
                logger.error(f"Не удалось решить задачу {task_id} на попытке {attempt_num + 1} (таймаут или ошибка вставки токена)")
                # Продолжаем к следующей основной попытке, если есть
                await asyncio.sleep(2)
                continue # К следующей основной попытке

        except Exception as e_main_try:
            logger.error(f"Ошибка в основной попытке {attempt_num + 1} решения капчи: {e_main_try}", exc_info=True)
            if attempt_num < max_retries - 1:
                logger.info("Пауза перед следующей попыткой...")
                await asyncio.sleep(5)
            # Если это последняя попытка, цикл завершится и функция вернет False ниже

    logger.error("Все попытки решения капчи исчерпаны.")
    return False

async def run_test_parser():
    """Запускает тестовый парсер с выбором категории Individuals"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context(viewport={"width": 1280, "height": 800})
        page = await context.new_page()

        try:
            logger.info("=== Тестовый парсер CPA Quebec (категория Individuals) ===")
            logger.info(f"Переходим на {BASE_URL}")

            # Переходим на страницу
            await page.goto(BASE_URL, timeout=60000)
            await page.wait_for_load_state("domcontentloaded")

            # Закрываем cookie banner
            try:
                cookie_banner = page.locator("text=Tout refuser")
                if await cookie_banner.is_visible(timeout=5000):
                    await cookie_banner.click()
                    logger.info("Cookie banner закрыт")
                    await page.wait_for_timeout(2000)
            except:
                pass

            # Выбираем категорию "SMEs"
            if not await select_category(page, "SMEs"):
                logger.error("Не удалось выбрать категорию")
                return

            # Обрабатываем капчу
            logger.info("Решаем капчу...")
            if not await solve_captcha_simple(page):
                logger.error("Не удалось решить капчу")
                return

            # Ждем дольше после решения капчи
            logger.info("Ждем стабилизации после решения капчи...")
            await page.wait_for_timeout(5000)

            # Логируем состояние формы перед отправкой
            form_state = await page.evaluate("""
                () => {
                    const checkboxes = Array.from(document.querySelectorAll('input[type="checkbox"]'));
                    const checkedBoxes = checkboxes.filter(cb => cb.checked);

                    return {
                        totalCheckboxes: checkboxes.length,
                        checkedCheckboxes: checkedBoxes.length,
                        checkedDetails: checkedBoxes.map(cb => ({
                            id: cb.id,
                            name: cb.name,
                            value: cb.value,
                            label: cb.closest('label') ? cb.closest('label').textContent.trim() : 'No label'
                        }))
                    };
                }
            """)
            logger.info(f"Состояние формы перед отправкой: {form_state}")

            # АНАЛИЗ ФОРМ НА СТРАНИЦЕ
            logger.info("=== АНАЛИЗ ФОРМ НА СТРАНИЦЕ ===")
            all_forms = await page.evaluate("""
                () => {
                    const forms = Array.from(document.querySelectorAll('form'));
                    return forms.map((form, index) => {
                        const checkboxes = form.querySelectorAll('input[type="checkbox"]');
                        const buttons = form.querySelectorAll('button, input[type="submit"], input[type="button"]');

                        return {
                            index: index,
                            id: form.id || 'NO_ID',
                            action: form.action || 'NO_ACTION',
                            method: form.method || 'NO_METHOD',
                            checkboxCount: checkboxes.length,
                            buttonCount: buttons.length
                        };
                    });
                }
            """)

            for form_info in all_forms:
                logger.info(f"Форма #{form_info['index']}: id={form_info['id']} | "
                           f"action={form_info['action']} | checkboxes={form_info['checkboxCount']} | "
                           f"buttons={form_info['buttonCount']}")

            # ГЛУБОКИЙ АНАЛИЗ КАПЧИ И ПОИСК ПРАВИЛЬНОЙ КНОПКИ
            logger.info("🔍 АНАЛИЗИРУЕМ КАПЧУ И ИЩЕМ ПРАВИЛЬНУЮ КНОПКУ...")

            captcha_and_button_analysis = await page.evaluate("""
                () => {
                    const result = {
                        captcha: {},
                        buttons: [],
                        form: {}
                    };

                    // 1. ДЕТАЛЬНЫЙ АНАЛИЗ КАПЧИ
                    const recaptchaDiv = document.querySelector('.g-recaptcha');
                    const recaptchaResponse = document.querySelector('#g-recaptcha-response');
                    const recaptchaIframes = document.querySelectorAll('iframe[src*="recaptcha"]');

                    result.captcha = {
                        divExists: !!recaptchaDiv,
                        responseExists: !!recaptchaResponse,
                        responseValue: recaptchaResponse ? recaptchaResponse.value : null,
                        responseLength: recaptchaResponse ? recaptchaResponse.value.length : 0,
                        iframeCount: recaptchaIframes.length,
                        sitekey: recaptchaDiv ? recaptchaDiv.getAttribute('data-sitekey') : null
                    };

                    // 2. ПОИСК ВСЕХ КНОПОК, СВЯЗАННЫХ С ФОРМОЙ CPA
                    const cpaForm = document.getElementById('FindACPABottinForm');
                    if (cpaForm) {
                        result.form = {
                            exists: true,
                            action: cpaForm.action,
                            method: cpaForm.method,
                            buttonsInside: cpaForm.querySelectorAll('button, input[type="submit"], input[type="button"]').length
                        };

                        // Ищем кнопки ВНЕ формы, но связанные с ней
                        const allButtons = document.querySelectorAll('button, input[type="submit"], input[type="button"]');
                        allButtons.forEach((btn, index) => {
                            const isVisible = btn.offsetParent !== null;
                            const isEnabled = !btn.disabled;
                            const formAttr = btn.getAttribute('form');
                            const isInCpaForm = cpaForm.contains(btn);
                            const onClick = btn.getAttribute('onclick');
                            const text = btn.textContent || btn.value || 'NO_TEXT';

                            // Проверяем, связана ли кнопка с формой CPA (СТРОГАЯ ПРОВЕРКА)
                            const isRelatedToCpaForm = isInCpaForm ||
                                                    formAttr === 'FindACPABottinForm' ||
                                                    (onClick && onClick.includes('FindACPA'));

                            if (isRelatedToCpaForm || (isVisible && isEnabled && text.toLowerCase().includes('search'))) {
                                result.buttons.push({
                                    index: index,
                                    text: text,
                                    type: btn.type,
                                    id: btn.id || 'NO_ID',
                                    className: btn.className,
                                    visible: isVisible,
                                    enabled: isEnabled,
                                    inCpaForm: isInCpaForm,
                                    formAttr: formAttr,
                                    onClick: onClick,
                                    position: {
                                        left: Math.round(btn.getBoundingClientRect().left),
                                        top: Math.round(btn.getBoundingClientRect().top)
                                    }
                                });
                            }
                        });
                    }

                    return result;
                }
            """)

            logger.info("=== АНАЛИЗ КАПЧИ ===")
            captcha_info = captcha_and_button_analysis['captcha']
            logger.info(f"Div капчи: {captcha_info['divExists']}")
            logger.info(f"Поле ответа: {captcha_info['responseExists']}")
            logger.info(f"Длина токена: {captcha_info['responseLength']}")
            logger.info(f"Количество iframe: {captcha_info['iframeCount']}")
            logger.info(f"Sitekey: {captcha_info['sitekey'][:10] if captcha_info['sitekey'] else 'None'}...")

            logger.info("=== АНАЛИЗ КНОПОК ===")
            buttons_info = captcha_and_button_analysis['buttons']
            logger.info(f"Найдено связанных кнопок: {len(buttons_info)}")

            for btn in buttons_info:
                logger.info(f"Кнопка: '{btn['text']}' | id={btn['id']} | "
                           f"visible={btn['visible']} | enabled={btn['enabled']} | "
                           f"inForm={btn['inCpaForm']} | formAttr={btn['formAttr']} | "
                           f"pos=({btn['position']['left']}, {btn['position']['top']})")

            # ПРОВЕРЯЕМ СОСТОЯНИЕ КАПЧИ
            if captcha_info['responseLength'] < 100:
                logger.error("❌ Токен капчи слишком короткий или отсутствует!")
                return

            # ЕСЛИ НЕТ КНОПОК, ИЩЕМ АЛЬТЕРНАТИВНЫЕ СПОСОБЫ ОТПРАВКИ
            if not buttons_info:
                logger.warning("⚠️ Не найдено кнопок, связанных с формой CPA!")
                logger.info("🔍 Ищем альтернативные способы отправки формы...")

                # Попробуем найти JavaScript события или триггеры
                alternative_submit = await page.evaluate("""
                    () => {
                        const cpaForm = document.getElementById('FindACPABottinForm');
                        if (!cpaForm) return { success: false, reason: 'Форма не найдена' };

                        // 1. Проверяем, есть ли обработчики событий на форме
                        const formEvents = [];
                        ['submit', 'change', 'input'].forEach(eventType => {
                            const listeners = getEventListeners ? getEventListeners(cpaForm)[eventType] : [];
                            if (listeners && listeners.length > 0) {
                                formEvents.push(eventType);
                            }
                        });

                        // 2. Проверяем, есть ли автоотправка при изменении чекбоксов
                        const checkboxes = cpaForm.querySelectorAll('input[type="checkbox"]');
                        let hasAutoSubmit = false;
                        checkboxes.forEach(cb => {
                            if (cb.getAttribute('onchange') || cb.getAttribute('onclick')) {
                                hasAutoSubmit = true;
                            }
                        });

                        // 3. Пробуем отправить форму напрямую
                        try {
                            console.log('🚀 Пробуем отправить форму CPA напрямую...');

                            // Убеждаемся, что токен капчи в форме
                            const recaptchaResponse = document.querySelector('#g-recaptcha-response');
                            if (recaptchaResponse && recaptchaResponse.value.length > 100) {
                                // Создаем скрытое поле в форме для токена
                                let hiddenToken = cpaForm.querySelector('input[name="g-recaptcha-response"]');
                                if (!hiddenToken) {
                                    hiddenToken = document.createElement('input');
                                    hiddenToken.type = 'hidden';
                                    hiddenToken.name = 'g-recaptcha-response';
                                    cpaForm.appendChild(hiddenToken);
                                }
                                hiddenToken.value = recaptchaResponse.value;
                                console.log('✅ Токен капчи добавлен в форму');
                            }

                            // Отправляем форму
                            cpaForm.submit();
                            console.log('✅ Форма отправлена через submit()');
                            return { success: true, method: 'direct_submit' };

                        } catch (e) {
                            console.error('❌ Ошибка при отправке формы:', e);
                            return { success: false, reason: e.message };
                        }
                    }
                """)

                if alternative_submit['success']:
                    logger.info(f"✅ Форма отправлена альтернативным способом: {alternative_submit['method']}")
                else:
                    logger.error(f"❌ Альтернативная отправка не удалась: {alternative_submit['reason']}")
                    return
            else:
                # ВЫБИРАЕМ ЛУЧШУЮ КНОПКУ (приоритет: в форме > с form атрибутом > видимая кнопка поиска)
                best_button = None
                for btn in buttons_info:
                    if btn['visible'] and btn['enabled']:
                        if btn['inCpaForm']:
                            best_button = btn
                            break
                        elif btn['formAttr'] == 'FindACPABottinForm':
                            best_button = btn
                            break
                        elif 'search' in btn['text'].lower() and not best_button:
                            best_button = btn

                if not best_button:
                    logger.error("❌ Не найдено подходящей кнопки для клика!")
                    return

                logger.info(f"🎯 Выбрана кнопка для клика: '{best_button['text']}' (id={best_button['id']})")

                # КЛИКАЕМ НА ВЫБРАННУЮ КНОПКУ
                logger.info("🚀 Кликаем на кнопку отправки...")
                click_success = await page.evaluate(f"""
                    (buttonIndex) => {{
                        const allButtons = document.querySelectorAll('button, input[type="submit"], input[type="button"]');
                        const targetButton = allButtons[buttonIndex];

                        if (targetButton) {{
                            console.log('🎯 Кликаем на кнопку:', targetButton.textContent || targetButton.value);

                            // Проверяем токен капчи перед кликом
                            const recaptchaResponse = document.querySelector('#g-recaptcha-response');
                            if (recaptchaResponse && recaptchaResponse.value.length > 100) {{
                                console.log('✅ Токен капчи готов, длина:', recaptchaResponse.value.length);
                            }} else {{
                                console.error('❌ Токен капчи не готов!');
                                return false;
                            }}

                            try {{
                                targetButton.click();
                                console.log('✅ Клик выполнен успешно');
                                return true;
                            }} catch (e) {{
                                console.error('❌ Ошибка при клике:', e);
                                return false;
                            }}
                        }} else {{
                            console.error('❌ Кнопка не найдена!');
                            return false;
                        }}
                    }}
                """, best_button['index'])

                if not click_success:
                    logger.error("❌ Не удалось кликнуть на кнопку!")
                    return

                logger.info("✅ Клик на кнопку выполнен успешно!")

            # Ждем навигации
            logger.info("⏳ Ожидаем навигацию...")
            try:
                # Ждем изменения URL или загрузки страницы
                await page.wait_for_url("**/FindACPABottinFormSubmit**", timeout=30000)
                logger.info("✅ Навигация завершена успешно")
            except Exception as nav_error:
                logger.warning(f"⚠️ Навигация не произошла или завершилась с ошибкой: {nav_error}")
                # Ждем немного и проверяем, изменился ли URL
                await page.wait_for_timeout(5000)
                current_url = page.url
                if "search" in current_url.lower() or current_url != BASE_URL:
                    logger.info(f"✅ URL изменился на: {current_url}, считаем навигацию успешной")
                else:
                    logger.warning("❌ URL не изменился, возможно навигация не произошла")

            logger.info("⏳ Ожидаем результаты...")

            # Ждем загрузки
            await page.wait_for_load_state("domcontentloaded")
            await page.wait_for_timeout(5000)

            # Анализируем результаты
            current_url = page.url
            logger.info(f"URL после поиска: {current_url}")

            # Получаем информацию о результатах
            results_info = await page.evaluate("""
                () => {
                    const pageText = document.body.innerText;
                    const url = window.location.href;

                    // Проверяем, находимся ли мы на странице результатов или снова на форме
                    const isFormPage = pageText.includes('Search criteria') ||
                                     pageText.includes('Last name of CPA') ||
                                     pageText.includes('First name of CPA') ||
                                     url.includes('/find-a-cpa/cpa-directory/');

                    const isResultsPage = url.includes('/search/') ||
                                        url.includes('/results/') ||
                                        url.includes('FindACPABottinFormSubmit');

                    // Ищем текст о количестве результатов
                    const resultMatches = pageText.match(/(\\d+)\\s*-\\s*(\\d+)\\s*of\\s*(\\d+)\\s*result/i);

                    // Ищем карточки CPA различными способами (обновленные селекторы)
                    const possibleSelectors = [
                        '.cpa-card', '.member-card', '.profile-card', '.directory-entry',
                        '.search-result', '.result-item', '.cpa-listing', '.member-listing',
                        '[data-cpa]', '[data-member]', '.contact-card', '.professional-card',
                        '.cpa-profile', '.professional-profile', '.directory-card',
                        '.member-info', '.cpa-info', '.professional-info',
                        'article.cpa-entry', 'div.profile-summary', 'li.item',
                        '.search-result-item', '.result-card'
                    ];

                    let foundCards = 0;
                    let cardSelector = null;

                    for (const selector of possibleSelectors) {
                        const elements = document.querySelectorAll(selector);
                        if (elements.length > 0) {
                            foundCards = elements.length;
                            cardSelector = selector;
                            break;
                        }
                    }

                    // Ищем любые элементы с email или телефоном
                    const emailElements = document.querySelectorAll('a[href^="mailto"]');
                    const phoneElements = document.querySelectorAll('a[href^="tel"]');

                    // Ищем элементы, содержащие имена (возможные CPA)
                    const nameElements = document.querySelectorAll('h1, h2, h3, h4, .name, .title, .professional-name');
                    let possibleNames = [];

                    nameElements.forEach(el => {
                        const text = el.textContent.trim();
                        // Простая проверка на имя (содержит буквы и возможно запятую)
                        if (text.length > 3 && text.length < 100 && /[A-Za-z]/.test(text)) {
                            possibleNames.push(text);
                        }
                    });

                    return {
                        url: url,
                        isFormPage: isFormPage,
                        isResultsPage: isResultsPage,
                        pageText: pageText.substring(0, 1500),
                        hasResultsText: pageText.toLowerCase().includes('result'),
                        resultMatches: resultMatches,
                        emailCount: emailElements.length,
                        phoneCount: phoneElements.length,
                        foundCards: foundCards,
                        cardSelector: cardSelector,
                        possibleNames: possibleNames.slice(0, 10), // Первые 10 имен
                        pageTitle: document.title
                    };
                }
            """)

            logger.info(f"Анализ результатов: {results_info}")

            # Проверяем, на какой странице мы находимся
            if results_info['isFormPage']:
                logger.error("❌ МЫ СНОВА НА СТРАНИЦЕ ФОРМЫ ПОИСКА!")
                logger.error("Это означает, что форма не была правильно отправлена или сервер вернул ошибку")
                logger.info("Возможные причины:")
                logger.info("1. Не все обязательные поля заполнены")
                logger.info("2. Капча не была полностью решена")
                logger.info("3. Сервер отклонил запрос")
                logger.info("4. Нужны дополнительные параметры формы")
            elif results_info['isResultsPage']:
                logger.info("✅ МЫ НА СТРАНИЦЕ РЕЗУЛЬТАТОВ!")

                if results_info['resultMatches']:
                    logger.info(f"📊 Найдены результаты: {results_info['resultMatches'][0]}")
                    logger.info(f"📧 Email ссылок: {results_info['emailCount']}")
                    logger.info(f"📞 Телефонных ссылок: {results_info['phoneCount']}")

                    if results_info['foundCards'] > 0:
                        logger.info(f"🎯 Найдено {results_info['foundCards']} карточек CPA с селектором: {results_info['cardSelector']}")

                    if results_info['possibleNames']:
                        logger.info(f"👥 Возможные имена CPA: {results_info['possibleNames'][:5]}")

                elif results_info['emailCount'] > 0 or results_info['phoneCount'] > 0:
                    logger.info(f"📧 Найдены контакты без явного счетчика результатов:")
                    logger.info(f"   Email ссылок: {results_info['emailCount']}")
                    logger.info(f"   Телефонных ссылок: {results_info['phoneCount']}")

                    if results_info['possibleNames']:
                        logger.info(f"👥 Возможные имена: {results_info['possibleNames'][:5]}")
                else:
                    logger.warning("⚠️ Страница результатов найдена, но данные CPA не обнаружены")
                    logger.info("Возможно, поиск не дал результатов для выбранной категории")
            else:
                logger.warning("❓ НЕИЗВЕСТНЫЙ ТИП СТРАНИЦЫ")
                logger.info(f"URL: {results_info['url']}")
                logger.info(f"Заголовок: {results_info['pageTitle']}")

            # Показываем часть текста страницы для диагностики
            logger.info("📄 Текст страницы (первые 1500 символов):")
            logger.info(results_info['pageText'])

            # Ждем для анализа
            input("\nПроанализируйте результаты в браузере и нажмите Enter...")

        except Exception as e:
            logger.error(f"Ошибка: {e}")
        finally:
            if anticaptcha_session and not anticaptcha_session.closed:
                await anticaptcha_session.close()
            await browser.close()

if __name__ == "__main__":
    asyncio.run(run_test_parser())